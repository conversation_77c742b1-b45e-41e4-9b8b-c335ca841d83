"""
Générateur de fichier Excel structuré pour le suivi des imports
Application de traitement et fusion de données MOAI et QGis

Auteur: Équipe de développement
Version: 2.0 - Optimisé pour démarrage rapide
Date: 2024
"""

# Configuration des couleurs
COLORS = {
    'PRIMARY': "#FF6600",      # Orange principal
    'PRIMARY_LIGHT': "#FF8533", # Orange clair
    'PRIMARY_DARK': "#CC5200",  # Orange foncé
    'SECONDARY': "#0066CC",     # Bleu
    'SECONDARY_LIGHT': "#3385D6", # Bleu clair
    'SUCCESS': "#28A745",       # Vert moderne
    'WARNING': "#FFC107",       # Jaune d'avertissement
    'DANGER': "#DC3545",        # Rouge d'erreur
    'INFO': "#6C757D",          # Gris informatif
    'LIGHT': "#F8F9FA",         # Gris très clair
    'WHITE': "#FFFFFF",         # Blanc pur
    'BORDER': "#DEE2E6",        # Bordure subtile
    'SHADOW': "#00000010",      # Ombre légère
    'BG': "#F5F6FA",           # Arrière-plan principal
    'CARD': "#FFFFFF"          # Arrière-plan des cartes
}

# Configuration des listes de validation
VALIDATION_LISTS = {
    "Domaine": ["Orange", "RIP"],
    "Type de Commune": ["Classique", "Fusionné"],
    "Type de base": ["Mono-Base", "Multi-Base"],
    "Motif Voie": ["Création Voie", "Modification Voie", "Rien à faire"],
    "STATUT Ticket": ["En cours", "Traité", "Rejeté", "Bloqué", "En Attente"],
    "Etat": ["En cours", "Livré", "En attente"],
    "Depose Ticket UPR": ["Non Créé", "Créé"],
    "PC Status": ["PC trouvé sur la voie", "PC fictif ajouté"],
    "XY Status": ["RAS", "MàJ XY effectué dans Oras", "Modification libélle 42C"],
    "Collaborateur": [
        "BACHOUEL Iheb",
        "BEN ALI Mariem",
        "ELJ Wissem",
        "OUESLATI Mohamed Amine",
        "ZAOUGA Wissem"
    ]
}

# Imports légers pour le démarrage rapide
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import re
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Imports lourds en lazy loading pour optimiser le démarrage
_pandas = None
_PIL_Image = None
_PIL_ImageTk = None

def get_pandas():
    """Lazy loading de pandas pour optimiser le démarrage."""
    global _pandas
    if _pandas is None:
        import pandas as pd
        _pandas = pd
    return _pandas

def get_PIL():
    """Lazy loading de PIL pour optimiser le démarrage."""
    global _PIL_Image, _PIL_ImageTk
    if _PIL_Image is None:
        from PIL import Image, ImageTk
        _PIL_Image = Image
        _PIL_ImageTk = ImageTk
    return _PIL_Image, _PIL_ImageTk

class ExcelStructuredFileGenerator:
    """
    Générateur de fichiers Excel structurés pour le traitement des données MOAI et QGis.

    Cette classe gère l'importation, le traitement et l'exportation de données
    provenant de fichiers MOAI et QGis vers un format Excel structuré.
    """

    # Constantes de configuration - Interface compacte avec footer toujours visible
    WINDOW_TITLE = "Générateur Suivi CM Adresse/ Plan Adressage"
    WINDOW_MIN_SIZE = "680x530"
    WINDOW_DEFAULT_SIZE = "700x550"

    # Typographie compacte pour petits écrans
    FONT_TITLE = ("Segoe UI", 10, "bold")
    FONT_SUBTITLE = ("Segoe UI", 9)
    FONT_BUTTON = ("Segoe UI", 9, "bold")
    FONT_HEADER = ("Segoe UI", 12, "bold")
    FONT_SUBHEADER = ("Segoe UI", 8)
    FONT_SMALL = ("Segoe UI", 7)

    LOGO_PATH = "logo-2024.png"

    # Colonnes à importer depuis QGis
    QGIS_COLUMNS = "A,B,C,D,J,O,P,Q,R"

    # Collaborateurs autorisés
    COLLABORATEURS = [
        "BACHOUEL Iheb",
        "BEN ALI Mariem",
        "ELJ Wissem",
        "OUESLATI Mohamed Amine",
        "ZAOUGA Wissem"
    ]

    def __init__(self, root):
        """
        Initialise l'interface graphique et les variables de l'application.
        Optimisé pour un démarrage rapide.

        Args:
            root: Fenêtre principale Tkinter
        """
        self.root = root
        self.root.title(self.WINDOW_TITLE)
        self.root.geometry(self.WINDOW_DEFAULT_SIZE)
        self.root.minsize(*map(int, self.WINDOW_MIN_SIZE.split('x')))
        self.root.configure(bg=COLORS['BG'])
        self.root.resizable(True, True)

        # Variables
        self.files = [None, None]  # [MOAI_file, QGis_file]
        self.logo_image = None
        self.logo_loaded = False

        # Configuration du style (optimisée)
        self._setup_styles_optimized()

        # Interface utilisateur (construction rapide)
        self._setup_ui()

        # Opérations asynchrones pour ne pas bloquer le démarrage
        self.root.after(10, self._post_init_async)

    def _post_init_async(self):
        """Opérations post-initialisation exécutées de manière asynchrone."""
        # Centrer la fenêtre
        self._center_window()

        # Charger le logo de manière asynchrone
        self._load_logo_async()

    def _center_window(self):
        """Centre la fenêtre sur l'écran de manière optimisée."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def _load_logo_async(self):
        """Charge le logo de manière asynchrone pour ne pas bloquer l'interface."""
        if not self.logo_loaded and hasattr(self, 'logo_placeholder'):
            try:
                if self._load_logo():
                    # Remplacer le placeholder par le vrai logo
                    self.logo_placeholder.configure(image=self.logo_image)
                    self.logo_loaded = True
            except Exception as e:
                print(f"Erreur lors du chargement asynchrone du logo: {e}")

    def _setup_styles_optimized(self):
        """Configuration optimisée des styles TTK pour un démarrage plus rapide."""
        try:
            style = ttk.Style()
            style.theme_use('clam')

            # Styles essentiels seulement au démarrage
            style.configure('Primary.TButton',
                          background=COLORS['PRIMARY'],
                          foreground='white',
                          font=self.FONT_BUTTON,
                          padding=(10, 5),
                          relief='flat',
                          borderwidth=0)

            style.configure('Success.TButton',
                          background=COLORS['SUCCESS'],
                          foreground='white',
                          font=self.FONT_BUTTON,
                          padding=(10, 5),
                          relief='flat',
                          borderwidth=0)

            # Styles additionnels chargés après
            self.root.after(50, self._setup_additional_styles)

        except Exception as e:
            print(f"Erreur lors de la configuration des styles: {e}")

    def _setup_additional_styles(self):
        """Charge les styles additionnels de manière asynchrone."""
        try:
            style = ttk.Style()

            style.map('Primary.TButton',
                     background=[('active', COLORS['PRIMARY_LIGHT']),
                               ('pressed', COLORS['PRIMARY_DARK'])])

            style.configure('Secondary.TButton',
                          background=COLORS['SECONDARY'],
                          foreground='white',
                          font=self.FONT_BUTTON,
                          padding=(10, 5),
                          relief='flat',
                          borderwidth=0)

            style.map('Secondary.TButton',
                     background=[('active', COLORS['SECONDARY_LIGHT']),
                               ('pressed', COLORS['SECONDARY'])])

            # Style pour les LabelFrame modernes
            style.configure('Card.TLabelframe',
                          background=COLORS['CARD'],
                          borderwidth=1,
                          relief='solid')

            style.configure('Card.TLabelframe.Label',
                          background=COLORS['CARD'],
                          foreground=COLORS['SECONDARY'],
                          font=self.FONT_TITLE)

            # Style pour les séparateurs
            style.configure('Modern.TSeparator',
                          background=COLORS['BORDER'])

        except Exception as e:
            print(f"Erreur lors de la configuration des styles additionnels: {e}")

    def _optimize_widget_creation(self):
        """Optimise la création des widgets pour un rendu plus fluide."""
        # Cette méthode peut être appelée pour optimiser la création de widgets complexes
        # en les créant par petits groupes avec des délais
        pass

    def _load_logo(self):
        """Charge et redimensionne le logo pour petits écrans avec lazy loading."""
        try:
            if not os.path.exists(self.LOGO_PATH):
                logging.warning(f"Logo non trouvé: {self.LOGO_PATH}")
                return False

            Image, ImageTk = get_PIL()  # Lazy loading de PIL
            image = Image.open(self.LOGO_PATH)
            # Logo élargi pour meilleure visibilité
            image = image.resize((120, 50), Image.Resampling.LANCZOS)
            self.logo_image = ImageTk.PhotoImage(image)
            logging.info("Logo chargé avec succès")
            return True

        except FileNotFoundError:
            logging.error(f"Fichier logo non trouvé: {self.LOGO_PATH}")
            return False
        except PermissionError:
            logging.error(f"Permission refusée pour accéder au logo: {self.LOGO_PATH}")
            return False
        except Exception as e:
            logging.error(f"Erreur lors du chargement du logo: {e}")
            return False

    def _setup_ui(self):
        """Configure l'interface utilisateur compacte sans scroll."""
        # Frame principal direct sans scrollbar pour interface compacte
        main_frame = tk.Frame(self.root, bg=COLORS['BG'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=5)

        # En-tête avec logo (plus compact)
        self._setup_header(main_frame)

        # Conteneur principal en deux colonnes pour optimiser l'espace
        columns_frame = tk.Frame(main_frame, bg=COLORS['BG'])
        columns_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 3))

        # Configuration des colonnes avec proportions optimisées pour éliminer l'espace vide
        columns_frame.grid_columnconfigure(0, weight=1, minsize=280)  # Colonne gauche - Import
        columns_frame.grid_columnconfigure(1, weight=1, minsize=280)  # Colonne droite - Projet + Génération

        # Colonne gauche : Import des fichiers
        left_column = tk.Frame(columns_frame, bg=COLORS['BG'])
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 3))

        # Colonne droite : Informations projet + Génération
        right_column = tk.Frame(columns_frame, bg=COLORS['BG'])
        right_column.grid(row=0, column=1, sticky="nsew", padx=(3, 0))

        # Section d'import des fichiers (colonne gauche)
        self._setup_file_import_section(left_column)

        # Section des champs de saisie (colonne droite)
        self._setup_input_fields(right_column)

        # Section de génération (colonne droite)
        self._setup_generation_section(right_column)

        # Pied de page compact
        self._setup_footer(main_frame)



    def _setup_header(self, parent):
        """Configure l'en-tête compact pour petits écrans."""
        # Conteneur principal avec effet de carte compact
        header_card = tk.Frame(parent, bg=COLORS['CARD'], relief='flat', bd=0)
        header_card.pack(fill=tk.X, pady=(0, 8), padx=3)

        # Ajouter une bordure subtile
        border_frame = tk.Frame(header_card, bg=COLORS['BORDER'], height=1)
        border_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # Contenu de l'en-tête avec padding réduit
        content_frame = tk.Frame(header_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=12, pady=8)

        # Section logo et titre
        logo_title_frame = tk.Frame(content_frame, bg=COLORS['CARD'])
        logo_title_frame.pack(fill=tk.X)

        # Placeholder pour le logo (chargement asynchrone)
        self.logo_placeholder = tk.Label(
            logo_title_frame,
            text="🏢",
            font=("Segoe UI", 24),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        self.logo_placeholder.pack(side=tk.LEFT, padx=(0, 10))

        # Section titre avec hiérarchie visuelle compacte
        title_section = tk.Frame(logo_title_frame, bg=COLORS['CARD'])
        title_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Titre principal compact
        main_title = tk.Label(
            title_section,
            text="Générateur Suivi CM Adresse - Plan Adressage",
            font=self.FONT_HEADER,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        main_title.pack(anchor=tk.W)

        # Sous-titre compact
        subtitle = tk.Label(
            title_section,
            text="Traitement automatisé MOAI et QGis",
            font=self.FONT_SUBHEADER,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        subtitle.pack(anchor=tk.W, pady=(1, 0))



        # Séparateur moderne
        ttk.Separator(parent, orient='horizontal', style='Modern.TSeparator').pack(fill=tk.X, pady=(0, 8))



    def _setup_file_import_section(self, parent):
        """Configure la section d'importation des fichiers avec design moderne."""
        # Carte principale pour l'import de fichiers compacte
        files_card = tk.Frame(parent, bg=COLORS['CARD'], relief='flat', bd=0)
        files_card.pack(fill=tk.X, pady=(0, 8), padx=3)

        # Bordure subtile
        border_frame = tk.Frame(files_card, bg=COLORS['BORDER'], height=1)
        border_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # En-tête de la section compact
        header_frame = tk.Frame(files_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=12, pady=(8, 5))

        tk.Label(
            header_frame,
            text="📁",
            font=("Segoe UI", 12),
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT, padx=(0, 5))

        tk.Label(
            header_frame,
            text="Import des fichiers",
            font=self.FONT_TITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        # Contenu des fichiers compact
        content_frame = tk.Frame(files_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=12, pady=(0, 8))

        # Section Export MOAI compacte
        moai_section = tk.Frame(content_frame, bg=COLORS['LIGHT'], relief='flat', bd=1)
        moai_section.pack(fill=tk.X, pady=(0, 5))

        moai_content = tk.Frame(moai_section, bg=COLORS['LIGHT'])
        moai_content.pack(fill=tk.X, padx=8, pady=6)

        # En-tête MOAI compact
        moai_header = tk.Frame(moai_content, bg=COLORS['LIGHT'])
        moai_header.pack(fill=tk.X, pady=(0, 3))

        tk.Label(
            moai_header,
            text="📊 Export MOAI",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['LIGHT']
        ).pack(side=tk.LEFT)

        # Bouton et statut MOAI compacts
        moai_action = tk.Frame(moai_content, bg=COLORS['LIGHT'])
        moai_action.pack(fill=tk.X)

        self.btn1 = ttk.Button(
            moai_action,
            text="Sélectionner",
            command=lambda: self.load_file(0),
            style='Primary.TButton'
        )
        self.btn1.pack(side=tk.LEFT, padx=(0, 8))

        self.label1 = tk.Label(
            moai_action,
            text="Aucun fichier sélectionné",
            font=self.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        self.label1.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Section QGis compacte
        qgis_section = tk.Frame(content_frame, bg=COLORS['LIGHT'], relief='flat', bd=1)
        qgis_section.pack(fill=tk.X)

        qgis_content = tk.Frame(qgis_section, bg=COLORS['LIGHT'])
        qgis_content.pack(fill=tk.X, padx=8, pady=6)

        # En-tête QGis compact
        qgis_header = tk.Frame(qgis_content, bg=COLORS['LIGHT'])
        qgis_header.pack(fill=tk.X, pady=(0, 3))

        tk.Label(
            qgis_header,
            text="🗺️ Résultats QGis",
            font=self.FONT_SUBTITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['LIGHT']
        ).pack(side=tk.LEFT)

        # Bouton et statut QGis compacts
        qgis_action = tk.Frame(qgis_content, bg=COLORS['LIGHT'])
        qgis_action.pack(fill=tk.X)

        self.btn2 = ttk.Button(
            qgis_action,
            text="Sélectionner",
            command=lambda: self.load_file(1),
            style='Primary.TButton'
        )
        self.btn2.pack(side=tk.LEFT, padx=(0, 8))

        self.label2 = tk.Label(
            qgis_action,
            text="Aucun fichier sélectionné",
            font=self.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        self.label2.pack(side=tk.LEFT, fill=tk.X, expand=True)

    def _setup_input_fields(self, parent):
        """Configure les champs de saisie avec design moderne de formulaire."""
        # Carte principale pour les informations du projet compacte
        input_card = tk.Frame(parent, bg=COLORS['CARD'], relief='flat', bd=0)
        input_card.pack(fill=tk.X, pady=(0, 8), padx=3)

        # Bordure subtile
        border_frame = tk.Frame(input_card, bg=COLORS['BORDER'], height=1)
        border_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # En-tête de la section compact
        header_frame = tk.Frame(input_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=12, pady=(8, 5))

        tk.Label(
            header_frame,
            text="✏️",
            font=("Segoe UI", 12),
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT, padx=(0, 5))

        tk.Label(
            header_frame,
            text="Informations du projet",
            font=self.FONT_TITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        # Contenu du formulaire compact
        form_frame = tk.Frame(input_card, bg=COLORS['CARD'])
        form_frame.pack(fill=tk.X, padx=12, pady=(0, 8))

        # Configuration de la grille
        form_frame.grid_columnconfigure(1, weight=1)

        # Champ Nom de la commune (auto-rempli)
        self._create_form_field(
            form_frame, 0,
            "Nom de la commune",
            "entry_commune",
            readonly=True,
            icon="🏘️",
            placeholder="Récupéré automatiquement du fichier MOAI"
        )

        # Champ Code INSEE (auto-rempli)
        self._create_form_field(
            form_frame, 1,
            "Code INSEE",
            "entry_insee",
            readonly=True,
            icon="🏛️",
            placeholder="Récupéré automatiquement du fichier MOAI"
        )

        # Champ ID tâche Plan Adressage (requis)
        self._create_form_field(
            form_frame, 2,
            "ID tâche Plan Adressage",
            "entry_id_tache",
            readonly=False,
            icon="📋",
            placeholder="Saisir l'ID de la tâche Plan Adressage",
            required=True
        )

        # Note explicative compacte
        note_frame = tk.Frame(form_frame, bg=COLORS['LIGHT'], relief='flat', bd=1)
        note_frame.grid(row=3, column=0, columnspan=2, sticky=tk.EW, pady=(8, 0))

        note_content = tk.Frame(note_frame, bg=COLORS['LIGHT'])
        note_content.pack(fill=tk.X, padx=8, pady=4)

        tk.Label(
            note_content,
            text="ℹ️",
            font=("Segoe UI", 10),
            fg=COLORS['SECONDARY'],
            bg=COLORS['LIGHT']
        ).pack(side=tk.LEFT, padx=(0, 5))

        tk.Label(
            note_content,
            text="Champs 🔒 = auto-remplis",
            font=self.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        ).pack(side=tk.LEFT)

    def _create_form_field(self, parent, row, label_text, entry_attr, readonly=False,
                          icon="", placeholder="", required=False):
        """Crée un champ de formulaire compact avec design cohérent."""
        # Conteneur du champ compact
        field_frame = tk.Frame(parent, bg=COLORS['CARD'])
        field_frame.grid(row=row, column=0, columnspan=2, sticky=tk.EW, pady=(0, 6))
        field_frame.grid_columnconfigure(1, weight=1)

        # Label avec icône compact
        label_frame = tk.Frame(field_frame, bg=COLORS['CARD'])
        label_frame.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 2))

        # Icône et texte du label compacts
        if icon:
            tk.Label(
                label_frame,
                text=icon,
                font=("Segoe UI", 9),
                fg=COLORS['SECONDARY'],
                bg=COLORS['CARD']
            ).pack(side=tk.LEFT, padx=(0, 3))

        label_color = COLORS['PRIMARY'] if required else COLORS['SECONDARY']
        label_font = self.FONT_SMALL if not required else self.FONT_SUBTITLE

        tk.Label(
            label_frame,
            text=label_text + (" *" if required else ""),
            font=label_font,
            fg=label_color,
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        if readonly:
            tk.Label(
                label_frame,
                text="🔒",
                font=("Segoe UI", 7),
                fg=COLORS['INFO'],
                bg=COLORS['CARD']
            ).pack(side=tk.RIGHT)

        # Champ de saisie compact
        entry_bg = COLORS['LIGHT'] if readonly else COLORS['WHITE']
        entry_state = 'readonly' if readonly else 'normal'

        entry = tk.Entry(
            field_frame,
            font=self.FONT_SMALL,
            bg=entry_bg,
            fg=COLORS['INFO'] if readonly else 'black',
            relief='flat',
            bd=1,
            state=entry_state,
            highlightthickness=1,
            highlightcolor=COLORS['PRIMARY'],
            highlightbackground=COLORS['BORDER']
        )
        entry.grid(row=1, column=0, columnspan=2, sticky=tk.EW, ipady=4)

        # Stocker la référence de l'entry
        setattr(self, entry_attr, entry)

        # Placeholder si le champ est vide
        if placeholder and readonly:
            entry.config(state='normal')
            entry.insert(0, placeholder)
            entry.config(state='readonly')

    def _setup_generation_section(self, parent):
        """Configure la section de génération avec design moderne et feedback visuel."""
        # Carte principale pour la génération compacte
        gen_card = tk.Frame(parent, bg=COLORS['CARD'], relief='flat', bd=0)
        gen_card.pack(fill=tk.X, pady=(0, 8), padx=3)

        # Bordure subtile
        border_frame = tk.Frame(gen_card, bg=COLORS['BORDER'], height=1)
        border_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # En-tête de la section compact
        header_frame = tk.Frame(gen_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=12, pady=(8, 5))

        tk.Label(
            header_frame,
            text="🚀",
            font=("Segoe UI", 12),
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT, padx=(0, 5))

        tk.Label(
            header_frame,
            text="Génération du fichier",
            font=self.FONT_TITLE,
            fg=COLORS['SECONDARY'],
            bg=COLORS['CARD']
        ).pack(side=tk.LEFT)

        # Contenu de la génération compact
        content_frame = tk.Frame(gen_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=12, pady=(0, 8))

        # Zone de statut compacte
        status_section = tk.Frame(content_frame, bg=COLORS['LIGHT'], relief='flat', bd=1)
        status_section.pack(fill=tk.X, pady=(0, 5))

        status_content = tk.Frame(status_section, bg=COLORS['LIGHT'])
        status_content.pack(fill=tk.X, padx=8, pady=6)

        # Indicateur de statut avec icône compact
        self.status_frame = tk.Frame(status_content, bg=COLORS['LIGHT'])
        self.status_frame.pack(fill=tk.X)

        self.status_icon = tk.Label(
            self.status_frame,
            text="⏳",
            font=("Segoe UI", 12),
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        self.status_icon.pack(side=tk.LEFT, padx=(0, 5))

        self.status_label = tk.Label(
            self.status_frame,
            text="En attente des fichiers...",
            font=self.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Section du bouton de génération compact
        button_section = tk.Frame(content_frame, bg=COLORS['CARD'])
        button_section.pack(fill=tk.X)

        # Bouton de génération principal compact
        self.combine_button = ttk.Button(
            button_section,
            text="📄 Générer Excel",
            command=self.export_to_excel,
            style='Success.TButton',
            state=tk.DISABLED
        )
        self.combine_button.pack(pady=3)

        # Texte d'aide sous le bouton compact
        help_text = tk.Label(
            button_section,
            text="3 feuilles : CM Adresse, Plan Adressage, Informations Commune",
            font=self.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD'],
            wraplength=300,
            justify=tk.CENTER
        )
        help_text.pack(pady=(2, 0))

    def _setup_footer(self, parent):
        """Configure le pied de page compact."""
        # Séparateur avant le footer
        ttk.Separator(parent, orient='horizontal', style='Modern.TSeparator').pack(fill=tk.X, pady=(8, 0))

        # Footer compact
        footer_frame = tk.Frame(parent, bg=COLORS['BG'])
        footer_frame.pack(fill=tk.X, pady=(8, 5))

        # Contenu du footer centré compact
        footer_content = tk.Frame(footer_frame, bg=COLORS['BG'])
        footer_content.pack()

        # Texte de copyright compact sans logo
        tk.Label(
            footer_content,
            text="© 2025 Sofrecom Tunisie- Equipe BLI ",
            font=("Segoe UI", 7),
            fg=COLORS['INFO'],
            bg=COLORS['BG']
        ).pack()

    def load_file(self, index: int):
        """
        Charge un fichier Excel et met à jour l'interface.

        Args:
            index: 0 pour MOAI, 1 pour QGis
        """
        try:
            filetypes = [("Fichiers Excel", "*.xlsx *.xls")]
            file_path = filedialog.askopenfilename(filetypes=filetypes)

            if not file_path:
                logging.info("Aucun fichier sélectionné")
                return

            if not os.path.exists(file_path):
                logging.error(f"Fichier non trouvé: {file_path}")
                messagebox.showerror("Erreur", f"Le fichier n'existe pas:\n{file_path}")
                return

            if not os.access(file_path, os.R_OK):
                logging.error(f"Permission de lecture refusée: {file_path}")
                messagebox.showerror("Erreur", f"Permission de lecture refusée:\n{file_path}")
                return

            self.files[index] = file_path
            filename = os.path.basename(file_path)

            if index == 0:  # Fichier MOAI
                self.label1.config(
                    text=f"✅ {filename[:50]}{'...' if len(filename) > 50 else ''}",
                    fg=COLORS['SUCCESS']
                )
                self._extract_insee_from_filename(file_path)
                self.btn1.config(text="✅ MOAI OK")
                logging.info(f"Fichier MOAI chargé: {filename}")

            elif index == 1:  # Fichier QGis
                self.label2.config(
                    text=f"✅ {filename[:40]}{'...' if len(filename) > 40 else ''}",
                    fg=COLORS['SUCCESS']
                )
                self.btn2.config(text="✅ QGis OK")
                logging.info(f"Fichier QGis chargé: {filename}")

            self._update_status()

        except Exception as e:
            logging.error(f"Erreur lors du chargement du fichier: {e}")
            messagebox.showerror("Erreur", f"Une erreur est survenue lors du chargement du fichier:\n{e}")

    def _update_status(self):
        """Met à jour le statut de l'application avec feedback visuel moderne."""
        if all(self.files):
            # Tous les fichiers sont chargés - prêt à générer
            self.status_icon.config(text="✅", fg=COLORS['SUCCESS'])
            self.status_label.config(
                text="Prêt à générer le fichier Excel",
                fg=COLORS['SUCCESS']
            )
            self.combine_button.config(state=tk.NORMAL)

            # Changer le style du bouton pour indiquer qu'il est actif
            self.combine_button.config(style='Success.TButton')

        elif any(self.files):
            # Certains fichiers sont chargés mais pas tous
            self.status_icon.config(text="⚠️", fg=COLORS['WARNING'])
            self.status_label.config(
                text="Veuillez sélectionner tous les fichiers requis",
                fg=COLORS['WARNING']
            )
            self.combine_button.config(state=tk.DISABLED)

        else:
            # Aucun fichier chargé
            self.status_icon.config(text="⏳", fg=COLORS['INFO'])
            self.status_label.config(
                text="En attente des fichiers...",
                fg=COLORS['INFO']
            )
            self.combine_button.config(state=tk.DISABLED)

    def _extract_insee_from_filename(self, file_path: str):
        """
        Extrait automatiquement le code INSEE et le nom de commune depuis le nom du fichier MOAI.

        Format attendu: insee_Fiabilisation_voies_nomcommune_date_time_matrice_globale.xlsx
        Exemple: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx

        Args:
            file_path: Chemin vers le fichier MOAI
        """
        try:
            filename = os.path.basename(file_path)

            if '_' not in filename:
                print("Format de nom de fichier non reconnu pour l'extraction automatique")
                return

            parts = filename.split('_')

            # Extraction du code INSEE (première partie)
            insee_part = parts[0]
            if self._is_valid_insee(insee_part):
                self._update_insee_field(insee_part)
                print(f"Code INSEE extrait automatiquement: {insee_part}")

                # Extraction du nom de commune (4ème partie si disponible)
                if len(parts) >= 4:
                    commune_name = self._extract_commune_name(parts[3])
                    if commune_name:
                        self._update_commune_field(commune_name)
                        print(f"Nom de commune extrait automatiquement: {commune_name}")
            else:
                print(f"Format de code INSEE non reconnu: {insee_part}")

        except Exception as e:
            print(f"Erreur lors de l'extraction automatique: {e}")

    def _is_valid_insee(self, insee_code: str) -> bool:
        """Vérifie si le code INSEE est valide (5 chiffres)."""
        return insee_code.isdigit() and len(insee_code) == 5

    def _extract_commune_name(self, commune_part: str) -> str:
        """Extrait et nettoie le nom de commune."""
        # Nettoyer le nom (enlever les chiffres de date s'ils sont collés)
        commune_clean = re.sub(r'\d{8}.*', '', commune_part)
        return commune_clean.upper() if commune_clean and len(commune_clean) > 1 else ""

    def _update_insee_field(self, insee: str):
        """Met à jour le champ INSEE."""
        self.entry_insee.config(state='normal')
        self.entry_insee.delete(0, tk.END)
        self.entry_insee.insert(0, insee)
        self.entry_insee.config(state='readonly')

    def _update_commune_field(self, commune: str):
        """Met à jour le champ commune."""
        self.entry_commune.config(state='normal')
        self.entry_commune.delete(0, tk.END)
        self.entry_commune.insert(0, commune)
        self.entry_commune.config(state='readonly')

    def export_to_excel(self):
        try:
            # Lazy loading de pandas seulement quand nécessaire
            pd = get_pandas()

            # Vérification des champs requis
            nom_commune = self.entry_commune.get().strip()
            id_tache = self.entry_id_tache.get().strip()
            insee = self.entry_insee.get().strip()

            if not (nom_commune and id_tache and insee):
                messagebox.showwarning("Champs manquants", "Veuillez remplir tous les champs : Nom Commune, ID Tâche et Code INSEE.")
                return

            # Lecture des données Export MOAI pour CM Adresse
            # Lire toutes les colonnes nécessaires : A (ref), G (Voie demandée), et Localité de la demande
            df_moai = pd.read_excel(self.files[0])

            # Essayer de lire avec la colonne U, sinon lire sans
            try:
                df_plan = pd.read_excel(self.files[1], usecols="A,B,C,D,J,O,P,Q,R,U")
                has_column_u = True
                print("✅ Colonne U trouvée et importée")
            except Exception as e:
                print(f"⚠️ Colonne U non trouvée, import sans colonne U: {e}")
                df_plan = pd.read_excel(self.files[1], usecols="A,B,C,D,J,O,P,Q,R")
                has_column_u = False

            # Renommer les colonnes de Plan Adressage avec les noms spécifiés
            if has_column_u:
                df_plan.columns = [
                    'Num Dossier Site',      # Colonne A du fichier source
                    'Num Voie Site',         # Colonne B du fichier source
                    'Comp Voie Site',        # Colonne C du fichier source
                    'Libelle Voie Site',     # Colonne D du fichier source
                    'Motif',                 # Colonne J du fichier source
                    'Même Adresse',          # Colonne O du fichier source
                    'Numero Voie BAN',       # Colonne P du fichier source
                    'Repondant Voie BAN',    # Colonne Q du fichier source
                    'Libelle Voie BAN',      # Colonne R du fichier source
                    'Adresse BAN'            # Colonne U du fichier source
                ]
            else:
                df_plan.columns = [
                    'Num Dossier Site',      # Colonne A du fichier source
                    'Num Voie Site',         # Colonne B du fichier source
                    'Comp Voie Site',        # Colonne C du fichier source
                    'Libelle Voie Site',     # Colonne D du fichier source
                    'Motif',                 # Colonne J du fichier source
                    'Même Adresse',          # Colonne O du fichier source
                    'Numero Voie BAN',       # Colonne P du fichier source
                    'Repondant Voie BAN',    # Colonne Q du fichier source
                    'Libelle Voie BAN'       # Colonne R du fichier source
                ]

            # Réorganiser les colonnes pour mettre "Même Adresse" avant Motif et "Colonne U" entre I et J
            if has_column_u:
                df_plan = df_plan[[
                    'Num Dossier Site',      # A
                    'Num Voie Site',         # B
                    'Comp Voie Site',        # C
                    'Libelle Voie Site',     # D
                    'Même Adresse',          # E (nouvelle position)
                    'Motif',                 # F (anciennement E)
                    'Numero Voie BAN',       # G
                    'Repondant Voie BAN',    # H
                    'Libelle Voie BAN',      # I
                    'Adresse BAN'            # J (nouvelle colonne entre I et J)
                ]]
            else:
                df_plan = df_plan[[
                    'Num Dossier Site',      # A
                    'Num Voie Site',         # B
                    'Comp Voie Site',        # C
                    'Libelle Voie Site',     # D
                    'Même Adresse',          # E (nouvelle position)
                    'Motif',                 # F (anciennement E)
                    'Numero Voie BAN',       # G
                    'Repondant Voie BAN',    # H
                    'Libelle Voie BAN'       # I
                ]]

            # Ajouter les nouveaux champs à la feuille Plan Adressage
            num_rows_plan = len(df_plan)

            if has_column_u:
                df_plan['Collaborateur'] = [''] * num_rows_plan  # K (anciennement J)
                df_plan['Date traitement'] = [''] * num_rows_plan  # L (anciennement K)
                df_plan['Durée'] = [''] * num_rows_plan  # M (anciennement L)
            else:
                df_plan['Collaborateur'] = [''] * num_rows_plan  # J (position originale)
                df_plan['Date traitement'] = [''] * num_rows_plan  # K (position originale)
                df_plan['Durée'] = [''] * num_rows_plan  # L (position originale)



            # Filtrer les lignes : exclure seulement celles qui ont "à analyser" dans le champ J (Motif)
            # ET qui ont simultanément les colonnes A, P, Q, R vides

            # Condition d'exclusion : lignes avec "à analyser" dans Motif ET colonnes A, P, Q, R toutes vides
            condition_exclusion = (
                # Vérifier si Motif contient "à analyser" (insensible à la casse)
                (df_plan['Motif'].astype(str).str.lower().str.contains('à analyser', na=False)) &
                # ET vérifier que les colonnes A, P, Q, R sont toutes vides
                (
                    (df_plan['Num Dossier Site'].isna() | (df_plan['Num Dossier Site'].astype(str).str.strip() == '')) &
                    (df_plan['Numero Voie BAN'].isna() | (df_plan['Numero Voie BAN'].astype(str).str.strip() == '')) &
                    (df_plan['Repondant Voie BAN'].isna() | (df_plan['Repondant Voie BAN'].astype(str).str.strip() == '')) &
                    (df_plan['Libelle Voie BAN'].isna() | (df_plan['Libelle Voie BAN'].astype(str).str.strip() == ''))
                )
            )

            # Garder toutes les lignes SAUF celles qui correspondent à la condition d'exclusion
            df_plan = df_plan[~condition_exclusion]





            # Construction du nom de fichier commençant par "Suivi"
            filename = f"Suivi_{nom_commune}_{id_tache}_{insee}.xlsx"
            save_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="Enregistrer le fichier Excel généré",
                initialfile=filename
            )

            if save_path:
                # Création de la feuille CM Adresse avec structure complète
                date_affectation = datetime.now().strftime("%d/%m/%Y")

                # Extraction des données depuis Export MOAI
                id_taches = df_moai.iloc[:, 0] if len(df_moai.columns) > 0 else []  # Colonne A (ref)
                voies_demandees = df_moai.iloc[:, 6] if len(df_moai.columns) > 6 else []  # Colonne G (Voie demandée)

                # Recherche de la colonne "Localité de la demande"
                localite_col = None
                for col in df_moai.columns:
                    if 'localité' in str(col).lower() and 'demande' in str(col).lower():
                        localite_col = col
                        break

                communes_moai = df_moai[localite_col] if localite_col else [nom_commune] * len(df_moai)

                # Création du DataFrame CM Adresse avec toutes les colonnes
                num_rows = len(df_moai)
                df_cm = pd.DataFrame({
                    'Domaine': [''] * num_rows,
                    'ID Tache': id_taches,
                    'Commune': communes_moai,
                    'Code INSEE': [insee] * num_rows,
                    'Type de Commune': [''] * num_rows,
                    'Type de base': [''] * num_rows,
                    'Voie demandé': voies_demandees,
                    'Motif Voie': [''] * num_rows,
                    'CODE RIVOLI': [''] * num_rows,
                    'GPS (X,Y)': [''] * num_rows,
                    'Centre/Zone': [''] * num_rows,
                    'Status PC': [''] * num_rows,  # Colonne L avec validation "PC trouvé sur la voie/ PC fictif ajouté"
                    'Descriptif Commentaire': [''] * num_rows,  # Colonne M avec validation "RAS / MàJ XY effectué dans Oras / Modification libélle 42C"
                    'Collaborateur': [''] * num_rows,
                    'Date affectation': [date_affectation] * num_rows,
                    'Date traitement': [''] * num_rows,
                    'Date livraison': [''] * num_rows,
                    'Durée': [''] * num_rows,
                    'STATUT Ticket': [''] * num_rows
                })

                # Création de la feuille avec les informations de la commune
                df_commune = pd.DataFrame({
                    'Nom de commune': [nom_commune],
                    'ID tâche Plan Adressage': [id_tache],  # Colonne avec l'ID tâche PA
                    'Code INSEE': [insee],
                    'Nbr des voies CM': [''],  # Sera calculé automatiquement
                    'Nbr des IMB PA': [''],  # Sera calculé automatiquement (valeurs uniques colonne A Plan Adressage)
                    'Date d\'affectation': [date_affectation],
                    'Date Livraison': [''],  # À remplir manuellement
                    'Etat Ticket PA ': [''],  # À remplir manuellement
                    'Durée Totale CM': [5],  # Initialisé à 5 min + sera incrémenté avec somme des durées de page 1
                    'Duréé Totale PA': [''],  # À remplir manuellement
                    'Durée Finale': [''],  # Sera calculée avec une formule Excel
                    'ID Tache 501/511': [''],  # À remplir manuellement
                    'Date Dépose Ticket 501/511': [''],  # À remplir manuellement
                    'Dépose Ticket UPR': ['Non Créé'],  # Nouvelle colonne avec valeur par défaut
                    'ID tâche UPR': [''],  # Nouvelle colonne avec valeur par défaut
                    'Collaborateur': ['']  # À remplir manuellement avec liste déroulante
                })

                # Créer les noms des feuilles avec l'ID tâche PA
                id_tache_clean = id_tache.replace(" ", "").replace("/", "-").replace("\\", "-")
                sheet_name_cm = f"{id_tache_clean}-CM Adresse"
                sheet_name_plan = f"{id_tache_clean}-Plan Adressage"
                sheet_name_commune = f"{id_tache_clean}-Informations Commune"

                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df_cm.to_excel(writer, sheet_name=sheet_name_cm, index=False)
                    df_plan.to_excel(writer, sheet_name=sheet_name_plan, index=False)
                    df_commune.to_excel(writer, sheet_name=sheet_name_commune, index=False)

                    # Ajout des validations de données (listes déroulantes)
                    self._add_data_validations(writer, df_cm, sheet_name_cm)

                    # Méthode alternative : créer une feuille avec les listes de validation
                    self._create_validation_sheet(writer)

                    # Ajouter la formule de calcul de durée totale
                    self._add_duration_formula(writer, len(df_cm), df_plan, sheet_name_cm, sheet_name_plan, sheet_name_commune)

                    # Ajouter des validations pour la feuille Informations Commune
                    self._add_commune_validations(writer, sheet_name_commune)

                    # Ajouter des validations pour la feuille Plan Adressage
                    self._add_plan_adressage_validations(writer, len(df_plan), sheet_name_plan)

                    # Ajouter des validations spécifiques pour la feuille CM Adresse (colonnes L et M)
                    self._add_cm_adresse_specific_validations(writer, len(df_cm), sheet_name_cm)

                    # Ajouter la mise en forme conditionnelle pour "Même Adresse"
                    self._add_conditional_formatting(writer, len(df_plan), sheet_name_plan)

                    # Ajouter la mise en forme conditionnelle pour les doublons dans Colonne U (si elle existe)
                    if has_column_u:
                        self._add_column_u_duplicate_formatting(writer, len(df_plan), sheet_name_plan, df_plan)

                    # Ajouter la mise en forme conditionnelle pour "Dépose Ticket UPR"
                    self._add_upr_conditional_formatting(writer, sheet_name_commune)



                    # Appliquer le centrage à toutes les feuilles
                    self._apply_center_alignment(writer, len(df_cm), len(df_plan), sheet_name_cm, sheet_name_plan, sheet_name_commune)

                messagebox.showinfo("Succès", f"Fichier généré avec succès :\n{save_path}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Une erreur est survenue :\n{e}")

    def _add_data_validations(self, writer, df_cm, sheet_name_cm):
        """Ajoute des listes de validation basées sur les données des fichiers Excel"""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            # Accès à la feuille CM Adresse
            worksheet = writer.sheets[sheet_name_cm]
            num_rows = len(df_cm)

            # Mapping des colonnes par nom pour éviter les erreurs de position
            column_mapping = {}
            for idx, col_name in enumerate(df_cm.columns):
                if idx < 26:  # Limiter aux colonnes A-Z
                    column_mapping[col_name] = chr(65 + idx)  # A, B, C, etc.

            logging.info(f"Colonnes détectées: {list(df_cm.columns)}")
            logging.info(f"Mapping colonnes: {column_mapping}")

            # Fonction helper pour créer une validation
            def add_validation(column_name, values_list):
                if column_name in column_mapping and values_list:
                    col_letter = column_mapping[column_name]
                    # Nettoyer les valeurs pour éviter les caractères problématiques
                    clean_values = []
                    for val in values_list:
                        clean_val = str(val).replace('"', '').replace(',', ';').strip()
                        if len(clean_val) > 0 and clean_val != 'nan' and clean_val != '':
                            clean_values.append(clean_val)

                    if clean_values:
                        try:
                            # Limiter à 10 éléments pour éviter les erreurs Excel
                            limited_values = clean_values[:10]
                            formula_str = ",".join(limited_values)

                            # Vérifier que la formule n'est pas trop longue
                            if len(formula_str) > 255:
                                limited_values = clean_values[:5]
                                formula_str = ",".join(limited_values)

                            dv = DataValidation(type="list", formula1=f'"{formula_str}"')
                            dv.add(f'{col_letter}2:{col_letter}{num_rows + 1}')
                            worksheet.add_data_validation(dv)
                            logging.info(f"Validation ajoutée pour {column_name} en colonne {col_letter}")
                            return True
                        except ValueError as ve:
                            logging.error(f"Erreur de validation pour {column_name}: {ve}")
                            return False
                        except Exception as e:
                            logging.error(f"Erreur inattendue pour {column_name}: {e}")
                            return False
                return False

            # Ajouter toutes les validations prédéfinies
            for col_name, values in VALIDATION_LISTS.items():
                add_validation(col_name, values)

        except KeyError as ke:
            logging.error(f"Erreur d'accès à la feuille Excel: {ke}")
            raise
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout des validations: {e}")
            raise

    def _create_validation_sheet(self, writer):
        """Crée une feuille cachée avec les listes de validation pour référence"""
        try:
            # Créer une feuille pour les listes de validation
            validation_data = {
                'Domaines': ["Orange ", "RIP", "", "", "", "", "", "", "", ""],
                'Types_Commune': ["Classique", "Fusionné", "", "", "", "", "", "", "", ""],
                'Types_Base': ["Mono-Base", "Multi-Base", "", "", "", "", "", "", "", ""],
                'Motifs_Voie': ["Création Voie", "Modification Voie", "Rien à faire", "", "", "", "", "", "", ""],
                'Statuts': ["En cours", "Traité", "Rejeté", "Bloqué", "En Attente", "", "", "", "", ""],
                'PC_Status': ["PC trouvé sur la voie", "PC fictif ajouté", "", "", "", "", "", "", "", ""],
                'XY_Status': ["RAS", "MàJ XY effectué dans Oras", "Modification libélle 42C", "", "", "", "", "", "", ""],
                'Collaborateurs': ["BACHOUEL Iheb", "BEN ALI Mariem", "ELJ Wissem", "OUESLATI Mohamed Amine", "ZAOUGA Wissem", "", "", "", "", ""]
            }

            pd = get_pandas()
            df_validation = pd.DataFrame(validation_data)
            df_validation.to_excel(writer, sheet_name="Listes_Validation", index=False)

            # Rendre la feuille cachée
            workbook = writer.book
            validation_sheet = workbook["Listes_Validation"]
            validation_sheet.sheet_state = 'hidden'



        except Exception as e:
            print(f"Erreur lors de la création de la feuille de validation: {e}")

    def _add_duration_formula(self, writer, num_rows_cm, df_plan, sheet_name_cm, sheet_name_plan, sheet_name_commune):
        """Ajoute les formules de calcul dans la feuille 'Informations Commune'"""
        try:
            # Accès aux feuilles
            worksheet_commune = writer.sheets[sheet_name_commune]
            worksheet_cm = writer.sheets[sheet_name_cm]

            # Trouver dynamiquement la colonne "Durée" dans CM Adresse
            duree_col_index_cm = None
            for col in range(1, 25):  # Vérifier jusqu'à la colonne Y
                cell_value = worksheet_cm.cell(row=1, column=col).value
                if cell_value == 'Durée':
                    duree_col_index_cm = col
                    duree_col_letter_cm = chr(64 + col)  # A=65, B=66, etc.
                    break

            # Trouver le nombre de lignes dans Plan Adressage
            num_rows_plan = len(df_plan)

            # Ajouter les formules
            if num_rows_cm > 0:
                # 1. Formule pour "Nbr des voies CM" (colonne D) = Nombre de lignes dans CM Adresse
                formula_nbr_voies = f'=COUNTA(\'{sheet_name_cm}\'!B2:B{num_rows_cm + 1})'
                worksheet_commune['D2'] = formula_nbr_voies

                # 2. Calcul automatique du nombre de valeurs uniques dans colonne A de Plan Adressage pour "Nbr des IMB PA"
                if num_rows_plan > 0 and 'Num Dossier Site' in df_plan.columns:
                    # Compter les valeurs uniques non-vides directement depuis le DataFrame
                    valeurs_colonne_a = df_plan['Num Dossier Site'].dropna()
                    valeurs_colonne_a = valeurs_colonne_a[valeurs_colonne_a.astype(str).str.strip() != '']
                    nbr_uniques = len(valeurs_colonne_a.unique())
                    worksheet_commune['E2'] = nbr_uniques
                else:
                    worksheet_commune['E2'] = 0

                # 3. Formule pour "Durée Totale CM" (colonne I) = 5 minutes de base + somme des durées de la page 1
                if duree_col_index_cm:
                    formula_cm = f'=5+SUM(\'{sheet_name_cm}\'!{duree_col_letter_cm}2:{duree_col_letter_cm}{num_rows_cm + 1})'
                    worksheet_commune['I2'] = formula_cm
                else:
                    worksheet_commune['I2'] = '=5'  # Valeur de base de 5 minutes si pas de colonne Durée

                # 4. Formule pour "Durée Totale PA" (colonne J) = Somme des durées dans Plan Adressage
                if num_rows_plan > 0:
                    # La colonne "Durée" dépend de la présence de la colonne U
                    # Avec Adresse BAN: M (13ème colonne), Sans Adresse BAN: L (12ème colonne)
                    duree_col_letter = 'M' if 'Adresse BAN' in df_plan.columns else 'L'
                    formula_pa = f'=SUM(\'{sheet_name_plan}\'!{duree_col_letter}2:{duree_col_letter}{num_rows_plan + 1})'
                    worksheet_commune['J2'] = formula_pa
                else:
                    worksheet_commune['J2'] = '=0'  # Valeur par défaut si pas de données Plan Adressage

                # 5. Formule pour "Durée Finale" (colonne K) = Durée Totale CM + Durée Totale PA
                formula_finale = '=I2+J2'  # I2 = Durée Totale CM, J2 = Durée Totale PA
                worksheet_commune['K2'] = formula_finale

            else:
                pass

        except Exception as e:
            print(f"Erreur lors de l'ajout des formules de durée: {e}")

    def _add_commune_validations(self, writer, sheet_name_commune):
        """Ajoute des validations de données pour la feuille 'Informations Commune'"""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            # Accès à la feuille Informations Commune
            worksheet = writer.sheets[sheet_name_commune]

            # Validation pour "Etat" (colonne H)
            etats = ["En cours", "Livré", "En attente"]
            dv_etat = DataValidation(type="list", formula1=f'"{",".join(etats)}"')
            dv_etat.add('H2:H10')  # Appliquer sur plusieurs lignes au cas où
            worksheet.add_data_validation(dv_etat)

            # Validation pour "Dépose Ticket UPR" (colonne N)
            depose_upr = ["Non Créé", "Créé"]
            dv_depose_upr = DataValidation(type="list", formula1=f'"{",".join(depose_upr)}"')
            dv_depose_upr.add('N2:N10')  # Appliquer sur plusieurs lignes au cas où
            worksheet.add_data_validation(dv_depose_upr)

            # Validation pour "Collaborateur" (colonne P - décalée à cause des nouvelles colonnes)
            collaborateurs = ["BACHOUEL Iheb", "BEN ALI Mariem", "ELJ Wissem", "OUESLATI Mohamed Amine", "ZAOUGA Wissem"]
            dv_collaborateur = DataValidation(type="list", formula1=f'"{",".join(collaborateurs)}"')
            dv_collaborateur.add('P2:P10')  # Appliquer sur plusieurs lignes au cas où
            worksheet.add_data_validation(dv_collaborateur)



        except Exception as e:
            print(f"Erreur lors de l'ajout des validations commune: {e}")

    def _add_plan_adressage_validations(self, writer, num_rows, sheet_name_plan):
        """Ajoute des validations de données pour la feuille 'Plan Adressage'"""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            # Accès à la feuille Plan Adressage
            worksheet = writer.sheets[sheet_name_plan]

            # Déterminer la colonne du Collaborateur selon la présence de la colonne U
            # Accès à la feuille pour vérifier les colonnes
            worksheet_plan = writer.sheets[sheet_name_plan]
            has_column_u_in_sheet = False
            for col in range(1, 15):  # Vérifier jusqu'à la colonne O
                cell_value = worksheet_plan.cell(row=1, column=col).value
                if cell_value == 'Adresse BAN':
                    has_column_u_in_sheet = True
                    break

            # Validation pour "Collaborateur"
            collaborateurs = ["BACHOUEL Iheb", "BEN ALI Mariem", "ELJ Wissem", "OUESLATI Mohamed Amine", "ZAOUGA Wissem"]
            dv_collaborateur = DataValidation(type="list", formula1=f'"{",".join(collaborateurs)}"')

            if has_column_u_in_sheet:
                # Avec colonne U: Collaborateur en K
                dv_collaborateur.add(f'K2:K{num_rows + 10}')
            else:
                # Sans colonne U: Collaborateur en J
                dv_collaborateur.add(f'J2:J{num_rows + 10}')

            worksheet.add_data_validation(dv_collaborateur)



        except Exception as e:
            print(f"Erreur lors de l'ajout des validations Plan Adressage: {e}")

    def _add_cm_adresse_specific_validations(self, writer, num_rows, sheet_name_cm):
        """Ajoute des validations spécifiques pour la feuille 'CM Adresse' (colonnes L et M)"""
        try:
            from openpyxl.worksheet.datavalidation import DataValidation

            # Accès à la feuille CM Adresse
            worksheet = writer.sheets[sheet_name_cm]

            # Validation pour "Status PC" (colonne L)
            pc_status_values = ["PC trouvé sur la voie", "PC fictif ajouté"]
            dv_pc_status = DataValidation(type="list", formula1=f'"{",".join(pc_status_values)}"')
            dv_pc_status.add(f'L2:L{num_rows + 10}')  # Colonne L
            worksheet.add_data_validation(dv_pc_status)

            # Validation pour "Descriptif Commentaire" (colonne M)
            descriptif_values = ["RAS", "MàJ XY effectué dans Oras", "Modification libélle 42C"]
            dv_descriptif = DataValidation(type="list", formula1=f'"{",".join(descriptif_values)}"')
            dv_descriptif.add(f'M2:M{num_rows + 10}')  # Colonne M
            worksheet.add_data_validation(dv_descriptif)



        except Exception as e:
            print(f"Erreur lors de l'ajout des validations spécifiques CM Adresse: {e}")

    def _add_conditional_formatting(self, writer, num_rows, sheet_name_plan):
        """Ajoute la mise en forme conditionnelle pour la colonne 'Même Adresse' et les doublons"""
        try:
            from openpyxl.formatting.rule import CellIsRule, FormulaRule
            from openpyxl.styles import PatternFill

            # Accès à la feuille Plan Adressage
            worksheet = writer.sheets[sheet_name_plan]

            # Définir les couleurs
            green_fill = PatternFill(start_color="e0fcd4", end_color="e0fcd4", fill_type="solid")  # Vert
            red_fill = PatternFill(start_color="f8dcdc", end_color="f8dcdc", fill_type="solid")    # Rouge
            duplicate_fill = PatternFill(start_color="b7dee8", end_color="b7dee8", fill_type="solid")  # Couleur pour doublons

            # === MISE EN FORME POUR "MÊME ADRESSE" (COLONNE E) ===

            # Règle pour "oui" en vert (colonne E)
            rule_oui = CellIsRule(operator='equal', formula=['"oui"'], fill=green_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_oui)

            # Règle pour "non" en rouge (colonne E)
            rule_non = CellIsRule(operator='equal', formula=['"non"'], fill=red_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_non)

            # Règles alternatives pour les variantes de casse
            rule_oui_maj = CellIsRule(operator='equal', formula=['"OUI"'], fill=green_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_oui_maj)

            rule_non_maj = CellIsRule(operator='equal', formula=['"NON"'], fill=red_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_non_maj)

            rule_oui_cap = CellIsRule(operator='equal', formula=['"Oui"'], fill=green_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_oui_cap)

            rule_non_cap = CellIsRule(operator='equal', formula=['"Non"'], fill=red_fill)
            worksheet.conditional_formatting.add(f'E2:E{num_rows + 10}', rule_non_cap)

            # === MISE EN FORME POUR LES DOUBLONS (COLONNE A) ===

            # Règle pour identifier les doublons dans la colonne A
            # Formule: COUNTIF($A$2:$A$1000,A2)>1 pour identifier les doublons
            duplicate_formula = f'COUNTIF($A$2:$A${num_rows + 10},A2)>1'
            rule_duplicates = FormulaRule(formula=[duplicate_formula], fill=duplicate_fill)
            worksheet.conditional_formatting.add(f'A2:A{num_rows + 10}', rule_duplicates)



        except Exception as e:
            print(f"Erreur lors de l'ajout de la mise en forme conditionnelle: {e}")

    def _add_upr_conditional_formatting(self, writer, sheet_name_commune):
        """
        Ajoute la mise en forme conditionnelle pour la colonne 'Dépose Ticket UPR'.

        Args:
            writer: ExcelWriter object
            sheet_name_commune: Nom de la feuille Informations Commune
        """
        try:
            from openpyxl.formatting.rule import CellIsRule
            from openpyxl.styles import PatternFill

            # Accès à la feuille Informations Commune
            worksheet = writer.sheets[sheet_name_commune]

            # Définir les couleurs
            green_fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")  # Vert
            red_fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")    # Rouge

            # === MISE EN FORME POUR "DÉPOSE TICKET UPR" (COLONNE N) ===

            # Règle pour "Créé" en vert
            rule_cree = CellIsRule(operator='equal', formula=['"Créé"'], fill=green_fill)
            worksheet.conditional_formatting.add('N2:N10', rule_cree)

            # Règle pour "Non Créé" en rouge
            rule_non_cree = CellIsRule(operator='equal', formula=['"Non Créé"'], fill=red_fill)
            worksheet.conditional_formatting.add('N2:N10', rule_non_cree)



        except Exception as e:
            print(f"Erreur lors de l'ajout de la mise en forme conditionnelle UPR: {e}")

    def _add_column_u_duplicate_formatting(self, writer, num_rows, sheet_name_plan, df_plan):
        """
        Ajoute la mise en forme conditionnelle pour les doublons dans la colonne Adresse BAN.
        Les doublons seront mis en évidence en gras rose (similaire à la logique de la colonne A).

        Args:
            writer: ExcelWriter object
            num_rows: Nombre de lignes dans Plan Adressage (non utilisé mais gardé pour cohérence)
            sheet_name_plan: Nom de la feuille Plan Adressage
            df_plan: DataFrame contenant les données Plan Adressage
        """
        try:
            from openpyxl.formatting.rule import FormulaRule
            from openpyxl.styles import PatternFill, Font

            # Accès à la feuille Plan Adressage
            worksheet = writer.sheets[sheet_name_plan]

            # Vérifier si la colonne Adresse BAN existe dans le DataFrame
            if 'Adresse BAN' not in df_plan.columns:
                return

            # Définir le style pour les doublons : fond rose et texte en gras (similaire à la colonne A)
            pink_fill = PatternFill(start_color="FFC0CB", end_color="FFC0CB", fill_type="solid")  # Rose
            bold_font = Font(bold=True)

            # === MISE EN FORME POUR LES DOUBLONS ADRESSE BAN (COLONNE J) ===
            # Utiliser la même logique que pour la colonne A
            # Règle pour identifier les doublons dans la colonne J (Adresse BAN)
            # Formule: COUNTIF($J$2:$J$1000,J2)>1 pour identifier les doublons
            duplicate_formula = f'COUNTIF($J$2:$J${num_rows + 10},J2)>1'
            rule_duplicates_adresse_ban = FormulaRule(formula=[duplicate_formula], fill=pink_fill, font=bold_font)
            worksheet.conditional_formatting.add(f'J2:J{num_rows + 10}', rule_duplicates_adresse_ban)





        except Exception as e:
            print(f"Erreur lors de l'ajout de la mise en forme des doublons Adresse BAN: {e}")

    def _apply_center_alignment(self, writer, num_rows_cm, num_rows_plan, sheet_name_cm, sheet_name_plan, sheet_name_commune):
        """
        Applique l'alignement centré à toutes les cellules de toutes les feuilles.

        Args:
            writer: ExcelWriter object
            num_rows_cm: Nombre de lignes dans CM Adresse
            num_rows_plan: Nombre de lignes dans Plan Adressage
            sheet_name_cm: Nom de la feuille CM Adresse
            sheet_name_plan: Nom de la feuille Plan Adressage
            sheet_name_commune: Nom de la feuille Informations Commune
        """
        try:
            from openpyxl.styles import Alignment

            # Définir l'alignement centré et aligné en bas sans renvoi à la ligne
            center_alignment = Alignment(
                horizontal='center',
                vertical='bottom',
                wrap_text=False
            )

            # Appliquer le centrage à chaque feuille
            self._center_worksheet(writer.sheets[sheet_name_cm], num_rows_cm + 1, center_alignment, "CM Adresse")
            self._center_worksheet(writer.sheets[sheet_name_plan], num_rows_plan + 1, center_alignment, "Plan Adressage")
            self._center_worksheet(writer.sheets[sheet_name_commune], 2, center_alignment, "Informations Commune")  # 1 ligne de données + en-tête



        except Exception as e:
            print(f"Erreur lors de l'application du centrage: {e}")

    def _center_worksheet(self, worksheet, num_rows, alignment, sheet_name):
        """
        Applique l'alignement centré à une feuille spécifique.

        Args:
            worksheet: Feuille de travail OpenPyXL
            num_rows: Nombre de lignes à traiter
            alignment: Style d'alignement
            sheet_name: Nom de la feuille (pour debug)
        """
        try:
            # Obtenir les dimensions de la feuille
            max_column = worksheet.max_column
            max_row = max(num_rows, worksheet.max_row)

            # Appliquer l'alignement à toutes les cellules utilisées
            for row in range(1, max_row + 1):
                for col in range(1, max_column + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.alignment = alignment

            # Ajuster la largeur des colonnes pour une meilleure lisibilité
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                # Définir une largeur optimale (minimum 12, maximum 50)
                adjusted_width = min(max(max_length + 2, 12), 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width



        except Exception as e:
            print(f"Erreur lors du centrage de la feuille '{sheet_name}': {e}")

# Lancement de l'application
def main():
    """Point d'entrée principal de l'application optimisée."""
    root = tk.Tk()
    # Optimisation : configuration initiale de la fenêtre pour un démarrage plus rapide
    root.withdraw()  # Cacher la fenêtre pendant l'initialisation

    app = ExcelStructuredFileGenerator(root)

    # Afficher la fenêtre une fois l'initialisation terminée
    root.deiconify()

    # Démarrer la boucle principale
    root.mainloop()

    # Nettoyage optionnel
    del app

if __name__ == "__main__":
    main()
